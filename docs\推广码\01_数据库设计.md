# 推广码和代理商分佣系统 - 数据库设计

## 📊 数据库表结构设计

### 1. 代理商表 (agents)

```sql
CREATE TABLE `agents` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '代理商ID',
  `agent_code` VARCHAR(20) UNIQUE NOT NULL COMMENT '代理商编码',
  `agent_name` VARCHAR(100) NOT NULL COMMENT '代理商名称',
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `contact_email` VARCHAR(100) COMMENT '联系邮箱',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `commission_rate_register` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '注册分佣比例',
  `commission_rate_print` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '打印分佣比例',
  `remark` TEXT COMMENT '备注信息',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` DATETIME NULL COMMENT '软删除时间',
  INDEX `idx_agent_code` (`agent_code`),
  INDEX `idx_status` (`status`),
  INDEX `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理商表';
```

**字段说明：**
- `agent_code`: 代理商唯一编码，用于标识代理商
- `commission_rate_register`: 注册分佣比例，如0.1000表示10%
- `commission_rate_print`: 打印分佣比例，如0.0500表示5%
- `delete_time`: 软删除时间，NULL表示未删除

### 2. 推广码表 (promotion_codes)

```sql
CREATE TABLE `promotion_codes` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '推广码ID',
  `agent_id` INT NOT NULL COMMENT '代理商ID',
  `code` VARCHAR(20) UNIQUE NOT NULL COMMENT '推广码',
  `name` VARCHAR(100) COMMENT '推广码名称',
  `description` TEXT COMMENT '推广码描述',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `max_usage` INT DEFAULT 0 COMMENT '最大使用次数，0表示无限制',
  `current_usage` INT DEFAULT 0 COMMENT '当前使用次数',
  `expire_time` DATETIME NULL COMMENT '过期时间，NULL表示永不过期',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` DATETIME NULL COMMENT '软删除时间',
  INDEX `idx_agent_id` (`agent_id`),
  INDEX `idx_code` (`code`),
  INDEX `idx_status` (`status`),
  INDEX `idx_expire_time` (`expire_time`),
  INDEX `idx_delete_time` (`delete_time`),
  FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广码表';
```

**字段说明：**
- `code`: 推广码，用户输入的推广码字符串
- `max_usage`: 最大使用次数限制，0表示无限制
- `current_usage`: 当前已使用次数，用于控制使用限制
- `expire_time`: 过期时间，NULL表示永不过期

### 3. 用户推广码绑定表 (user_promotion_bindings)

```sql
CREATE TABLE `user_promotion_bindings` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '绑定记录ID',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `promotion_code_id` INT NOT NULL COMMENT '推广码ID',
  `agent_id` INT NOT NULL COMMENT '代理商ID（冗余字段，便于查询）',
  `bind_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `unbind_time` DATETIME NULL COMMENT '解绑时间，NULL表示未解绑',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-已绑定，0-已解绑',
  `bind_source` VARCHAR(20) DEFAULT 'MANUAL' COMMENT '绑定来源：MANUAL-手动绑定，AUTO-自动绑定',
  `remark` TEXT COMMENT '备注信息',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_promotion_code_id` (`promotion_code_id`),
  INDEX `idx_agent_id` (`agent_id`),
  INDEX `idx_bind_time` (`bind_time`),
  INDEX `idx_status` (`status`),
  UNIQUE KEY `uk_user_active` (`user_id`, `status`),
  FOREIGN KEY (`user_id`) REFERENCES `user`(`userId`),
  FOREIGN KEY (`promotion_code_id`) REFERENCES `promotion_codes`(`id`),
  FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户推广码绑定表';
```

**字段说明：**
- `uk_user_active`: 唯一约束，确保一个用户同时只能绑定一个有效的推广码
- `bind_source`: 绑定来源，支持手动绑定和自动绑定
- `agent_id`: 冗余字段，便于快速查询用户对应的代理商

### 4. 分佣记录表 (commission_records)

```sql
CREATE TABLE `commission_records` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分佣记录ID',
  `agent_id` INT NOT NULL COMMENT '代理商ID',
  `promotion_code_id` INT NOT NULL COMMENT '推广码ID',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `commission_type` VARCHAR(20) NOT NULL COMMENT '分佣类型：REGISTER-注册，FIRST_PRINT-首次打印',
  `commission_amount` DECIMAL(10,2) NOT NULL COMMENT '分佣金额',
  `commission_rate` DECIMAL(5,4) NOT NULL COMMENT '分佣比例',
  `base_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '基础金额',
  `status` VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PENDING-待处理，CONFIRMED-已确认，PAID-已支付，CANCELLED-已取消',
  `business_id` VARCHAR(50) COMMENT '业务ID（如打印记录ID）',
  `business_type` VARCHAR(20) COMMENT '业务类型（如PRINT）',
  `settlement_batch` VARCHAR(50) COMMENT '结算批次号',
  `settlement_time` DATETIME NULL COMMENT '结算时间',
  `remark` TEXT COMMENT '备注信息',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_agent_id` (`agent_id`),
  INDEX `idx_promotion_code_id` (`promotion_code_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_commission_type` (`commission_type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`),
  INDEX `idx_settlement_batch` (`settlement_batch`),
  INDEX `idx_business` (`business_type`, `business_id`),
  FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`),
  FOREIGN KEY (`promotion_code_id`) REFERENCES `promotion_codes`(`id`),
  FOREIGN KEY (`user_id`) REFERENCES `user`(`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣记录表';
```

**字段说明：**
- `commission_type`: 分佣类型，支持注册分佣和首次打印分佣
- `base_amount`: 基础金额，用于计算分佣的基数
- `business_id`: 关联的业务记录ID，如打印记录ID
- `settlement_batch`: 结算批次号，用于批量结算

### 5. 分佣配置表 (commission_configs)

```sql
CREATE TABLE `commission_configs` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
  `config_key` VARCHAR(50) UNIQUE NOT NULL COMMENT '配置键',
  `config_value` TEXT NOT NULL COMMENT '配置值',
  `config_type` VARCHAR(20) NOT NULL COMMENT '配置类型：RATE-比例，AMOUNT-金额，RULE-规则',
  `description` VARCHAR(200) COMMENT '配置描述',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_config_key` (`config_key`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣配置表';
```

**字段说明：**
- `config_key`: 配置键，如'default_register_rate'、'default_print_amount'
- `config_value`: 配置值，JSON格式存储复杂配置
- `config_type`: 配置类型，便于程序解析

## 📈 索引优化策略

### 1. 查询优化索引
```sql
-- 用户绑定推广码查询优化
CREATE INDEX `idx_user_binding_active` ON `user_promotion_bindings`(`user_id`, `status`, `bind_time`);

-- 代理商分佣记录查询优化
CREATE INDEX `idx_agent_commission_time` ON `commission_records`(`agent_id`, `create_time`, `status`);

-- 推广码使用统计优化
CREATE INDEX `idx_code_usage_stats` ON `user_promotion_bindings`(`promotion_code_id`, `bind_time`);
```

### 2. 分区策略（可选）
对于大量数据的分佣记录表，可以考虑按时间分区：
```sql
-- 按月分区分佣记录表
ALTER TABLE `commission_records` 
PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 🔧 数据完整性约束

### 1. 业务规则约束
```sql
-- 确保分佣比例在合理范围内
ALTER TABLE `agents` ADD CONSTRAINT `chk_commission_rate_register` 
CHECK (`commission_rate_register` >= 0 AND `commission_rate_register` <= 1);

ALTER TABLE `agents` ADD CONSTRAINT `chk_commission_rate_print` 
CHECK (`commission_rate_print` >= 0 AND `commission_rate_print` <= 1);

-- 确保分佣金额为正数
ALTER TABLE `commission_records` ADD CONSTRAINT `chk_commission_amount` 
CHECK (`commission_amount` >= 0);
```

### 2. 状态值约束
```sql
-- 代理商状态约束
ALTER TABLE `agents` ADD CONSTRAINT `chk_agent_status` 
CHECK (`status` IN (0, 1));

-- 推广码状态约束
ALTER TABLE `promotion_codes` ADD CONSTRAINT `chk_promotion_status` 
CHECK (`status` IN (0, 1));

-- 分佣记录状态约束
ALTER TABLE `commission_records` ADD CONSTRAINT `chk_commission_status` 
CHECK (`status` IN ('PENDING', 'CONFIRMED', 'PAID', 'CANCELLED'));
```

## 📝 初始化数据

### 1. 默认分佣配置
```sql
INSERT INTO `commission_configs` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('default_register_rate', '0.1000', 'RATE', '默认注册分佣比例'),
('default_print_amount', '1.00', 'AMOUNT', '默认首次打印分佣金额'),
('max_promotion_codes_per_agent', '10', 'RULE', '每个代理商最大推广码数量'),
('promotion_code_length', '8', 'RULE', '推广码长度'),
('commission_settlement_cycle', '30', 'RULE', '分佣结算周期（天）');
```

### 2. 测试数据（开发环境）
```sql
-- 测试代理商
INSERT INTO `agents` (`agent_code`, `agent_name`, `contact_person`, `contact_phone`, `commission_rate_register`, `commission_rate_print`) VALUES
('AGENT001', '测试代理商A', '张三', '13800138001', 0.1000, 0.0500),
('AGENT002', '测试代理商B', '李四', '13800138002', 0.0800, 0.0300);

-- 测试推广码
INSERT INTO `promotion_codes` (`agent_id`, `code`, `name`, `description`) VALUES
(1, 'PROMO001', '代理商A推广码1', '测试推广码'),
(1, 'PROMO002', '代理商A推广码2', '测试推广码'),
(2, 'PROMO003', '代理商B推广码1', '测试推广码');
```

## 🔍 数据库迁移脚本

完整的数据库迁移脚本将在 `V2__create_promotion_system_tables.sql` 文件中提供，包括：
1. 表结构创建
2. 索引创建
3. 约束添加
4. 初始化数据插入
5. 验证脚本

---
*文档版本：v1.0*  
*创建时间：2025-07-29*  
*最后更新：2025-07-29*
