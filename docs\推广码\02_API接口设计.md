# 推广码和代理商分佣系统 - API接口设计

## 📋 API概览

### 接口分类
1. **用户端API** - 用户绑定推广码相关接口
2. **代理商端API** - 代理商管理推广码和查看分佣
3. **管理员端API** - 系统管理员管理代理商和系统配置

### 统一响应格式
所有API接口遵循项目现有的RetKit响应格式：
```json
{
  "success": true,
  "msg": "操作成功",
  "data": {
    // 具体数据
  }
}
```

## 🔧 用户端API

### 1. 绑定推广码
**接口路径：** `POST /api/v2/promotion/bindCode`  
**需要认证：** 是  
**请求参数：**
```json
{
  "promotionCode": "PROMO001"
}
```

**响应示例：**
```json
{
  "success": true,
  "msg": "推广码绑定成功",
  "data": {
    "bindingId": 123,
    "promotionCode": "PROMO001",
    "agentName": "测试代理商A",
    "bindTime": "2025-07-29 10:30:00"
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "msg": "推广码不存在或已失效"
}
```

### 2. 验证推广码
**接口路径：** `GET /api/v2/promotion/validateCode`  
**需要认证：** 否  
**请求参数：**
- `code`: 推广码

**响应示例：**
```json
{
  "success": true,
  "msg": "推广码有效",
  "data": {
    "valid": true,
    "agentName": "测试代理商A",
    "description": "测试推广码",
    "expireTime": null
  }
}
```

### 3. 查询绑定状态
**接口路径：** `GET /api/v2/promotion/bindingStatus`  
**需要认证：** 是  

**响应示例：**
```json
{
  "success": true,
  "msg": "查询成功",
  "data": {
    "hasBound": true,
    "promotionCode": "PROMO001",
    "agentName": "测试代理商A",
    "bindTime": "2025-07-29 10:30:00",
    "commissionEarned": 15.50
  }
}
```

### 4. 解绑推广码
**接口路径：** `POST /api/v2/promotion/unbindCode`  
**需要认证：** 是  

**响应示例：**
```json
{
  "success": true,
  "msg": "推广码解绑成功",
  "data": {
    "unbindTime": "2025-07-29 15:30:00"
  }
}
```

## 🏢 代理商端API

### 1. 代理商登录
**接口路径：** `POST /api/v2/agent/login`  
**需要认证：** 否  
**请求参数：**
```json
{
  "agentCode": "AGENT001",
  "password": "password123"
}
```

**响应示例：**
```json
{
  "success": true,
  "msg": "登录成功",
  "data": {
    "accessToken": "agent_token_xxx",
    "agentInfo": {
      "id": 1,
      "agentCode": "AGENT001",
      "agentName": "测试代理商A",
      "contactPerson": "张三"
    }
  }
}
```

### 2. 获取推广码列表
**接口路径：** `GET /api/v2/agent/promotionCodes`  
**需要认证：** 是（代理商）  
**请求参数：**
- `pageNumber`: 页码（默认1）
- `pageSize`: 页大小（默认10）
- `status`: 状态筛选（可选）

**响应示例：**
```json
{
  "success": true,
  "msg": "查询成功",
  "data": {
    "page": {
      "pageNumber": 1,
      "pageSize": 10,
      "totalPage": 1,
      "totalRow": 2,
      "list": [
        {
          "id": 1,
          "code": "PROMO001",
          "name": "推广码1",
          "status": 1,
          "currentUsage": 5,
          "maxUsage": 0,
          "createTime": "2025-07-29 09:00:00"
        }
      ]
    }
  }
}
```

### 3. 创建推广码
**接口路径：** `POST /api/v2/agent/promotionCodes`  
**需要认证：** 是（代理商）  
**请求参数：**
```json
{
  "name": "新推广码",
  "description": "推广码描述",
  "maxUsage": 100,
  "expireTime": "2025-12-31 23:59:59"
}
```

**响应示例：**
```json
{
  "success": true,
  "msg": "推广码创建成功",
  "data": {
    "id": 5,
    "code": "PROMO005",
    "name": "新推广码",
    "status": 1
  }
}
```

### 4. 分佣记录查询
**接口路径：** `GET /api/v2/agent/commissionRecords`  
**需要认证：** 是（代理商）  
**请求参数：**
- `pageNumber`: 页码
- `pageSize`: 页大小
- `commissionType`: 分佣类型（可选）
- `status`: 状态（可选）
- `startTime`: 开始时间（可选）
- `endTime`: 结束时间（可选）

**响应示例：**
```json
{
  "success": true,
  "msg": "查询成功",
  "data": {
    "page": {
      "list": [
        {
          "id": 1,
          "userId": 100,
          "userNickName": "用户A",
          "promotionCode": "PROMO001",
          "commissionType": "REGISTER",
          "commissionAmount": 10.00,
          "status": "CONFIRMED",
          "createTime": "2025-07-29 10:30:00"
        }
      ]
    },
    "summary": {
      "totalAmount": 150.50,
      "pendingAmount": 50.00,
      "confirmedAmount": 100.50
    }
  }
}
```

### 5. 推广数据统计
**接口路径：** `GET /api/v2/agent/statistics`  
**需要认证：** 是（代理商）  
**请求参数：**
- `period`: 统计周期（day/week/month/year）
- `startTime`: 开始时间（可选）
- `endTime`: 结束时间（可选）

**响应示例：**
```json
{
  "success": true,
  "msg": "查询成功",
  "data": {
    "overview": {
      "totalUsers": 50,
      "totalCommission": 500.00,
      "thisMonthUsers": 10,
      "thisMonthCommission": 100.00
    },
    "trends": [
      {
        "date": "2025-07-29",
        "newUsers": 3,
        "commission": 30.00
      }
    ],
    "promotionCodeStats": [
      {
        "code": "PROMO001",
        "users": 25,
        "commission": 250.00
      }
    ]
  }
}
```

## 👨‍💼 管理员端API

### 1. 代理商管理

#### 1.1 获取代理商列表
**接口路径：** `GET /api/v2/admin/agents`  
**需要认证：** 是（管理员）  

#### 1.2 创建代理商
**接口路径：** `POST /api/v2/admin/agents`  
**需要认证：** 是（管理员）  
**请求参数：**
```json
{
  "agentCode": "AGENT003",
  "agentName": "新代理商",
  "contactPerson": "王五",
  "contactPhone": "13800138003",
  "contactEmail": "<EMAIL>",
  "commissionRateRegister": 0.1000,
  "commissionRatePrint": 0.0500
}
```

#### 1.3 更新代理商信息
**接口路径：** `PUT /api/v2/admin/agents/{agentId}`  
**需要认证：** 是（管理员）  

#### 1.4 禁用/启用代理商
**接口路径：** `PUT /api/v2/admin/agents/{agentId}/status`  
**需要认证：** 是（管理员）  
**请求参数：**
```json
{
  "status": 0
}
```

### 2. 分佣管理

#### 2.1 分佣记录查询
**接口路径：** `GET /api/v2/admin/commissionRecords`  
**需要认证：** 是（管理员）  

#### 2.2 分佣统计报表
**接口路径：** `GET /api/v2/admin/commissionStatistics`  
**需要认证：** 是（管理员）  

#### 2.3 批量确认分佣
**接口路径：** `POST /api/v2/admin/commissionRecords/batchConfirm`  
**需要认证：** 是（管理员）  
**请求参数：**
```json
{
  "recordIds": [1, 2, 3, 4, 5],
  "settlementBatch": "BATCH_20250729_001"
}
```

### 3. 系统配置

#### 3.1 获取分佣配置
**接口路径：** `GET /api/v2/admin/commissionConfigs`  
**需要认证：** 是（管理员）  

#### 3.2 更新分佣配置
**接口路径：** `PUT /api/v2/admin/commissionConfigs`  
**需要认证：** 是（管理员）  
**请求参数：**
```json
{
  "configs": [
    {
      "configKey": "default_register_rate",
      "configValue": "0.1200"
    }
  ]
}
```

## 🔒 认证与权限

### 1. 用户认证
- 使用现有的用户认证体系
- 通过accessToken验证用户身份
- 继承AppController获取用户信息

### 2. 代理商认证
- 独立的代理商认证体系
- 代理商专用的accessToken
- 代理商权限验证拦截器

### 3. 管理员认证
- 使用现有的管理员认证体系
- 通过角色权限控制访问

## 📝 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 40001 | 推广码不存在 | 输入的推广码不存在 |
| 40002 | 推广码已失效 | 推广码已过期或被禁用 |
| 40003 | 推广码使用次数已达上限 | 推广码使用次数超过限制 |
| 40004 | 用户已绑定推广码 | 用户已经绑定了其他推广码 |
| 40005 | 用户未绑定推广码 | 用户尚未绑定任何推广码 |
| 40006 | 代理商不存在 | 代理商账号不存在 |
| 40007 | 代理商已被禁用 | 代理商账号已被禁用 |
| 40008 | 分佣记录不存在 | 指定的分佣记录不存在 |
| 40009 | 权限不足 | 没有执行该操作的权限 |
| 50001 | 分佣计算失败 | 分佣金额计算出现错误 |

## 🔧 接口实现注意事项

### 1. 性能优化
- 推广码验证接口考虑缓存
- 分佣记录查询使用分页
- 统计数据考虑定时预计算

### 2. 安全考虑
- 推广码绑定防刷机制
- 分佣记录防篡改
- 敏感操作记录日志

### 3. 事务处理
- 绑定推广码和生成分佣记录使用事务
- 批量操作使用事务保证一致性

---
*文档版本：v1.0*  
*创建时间：2025-07-29*  
*最后更新：2025-07-29*
