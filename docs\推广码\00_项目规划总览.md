# 推广码和代理商分佣系统 - 项目规划总览

## 📋 项目概述

### 核心需求
1. **用户绑定推广码**：用户注册后，在个人界面绑定代理商的"推广码"
2. **注册分佣**：用户绑定推广码后，对应代理商可以获得分佣
3. **首次打印分佣**：绑定了推广码的新用户首次打印时，对应代理商可以获得分佣

### 技术架构
- **后端框架**：JFinal
- **数据库**：MySQL
- **ORM**：JFinal ActiveRecord
- **认证体系**：集成现有用户认证系统

## 🗂️ 任务分解

### 第一阶段：基础架构（1-2周）

#### 1. 数据库设计与建表
- **负责人**：后端开发
- **工期**：2-3天
- **交付物**：
  - 数据库设计文档
  - 数据库迁移脚本
  - 表结构验证脚本

#### 2. 数据模型层开发
- **负责人**：后端开发
- **工期**：2天
- **交付物**：
  - Agent模型类
  - PromotionCode模型类
  - CommissionRecord模型类
  - 模型映射配置

### 第二阶段：核心业务（2-3周）

#### 3. 推广码管理服务
- **负责人**：后端开发
- **工期**：3天
- **交付物**：
  - 推广码生成算法
  - 推广码验证服务
  - 推广码状态管理

#### 4. 代理商管理服务
- **负责人**：后端开发
- **工期**：3天
- **交付物**：
  - 代理商注册服务
  - 代理商信息管理
  - 代理商推广码管理

#### 5. 分佣计算服务
- **负责人**：后端开发
- **工期**：4天
- **交付物**：
  - 分佣规则引擎
  - 分佣计算逻辑
  - 分佣记录生成

### 第三阶段：API接口（1-2周）

#### 6. 用户推广码绑定API
- **负责人**：后端开发
- **工期**：2天
- **交付物**：
  - 绑定推广码接口
  - 验证推广码接口
  - 查询绑定状态接口

#### 7. 分佣触发机制
- **负责人**：后端开发
- **工期**：3天
- **交付物**：
  - 注册分佣触发
  - 首次打印分佣触发
  - 异步分佣处理

### 第四阶段：管理功能（1周）

#### 8. 代理商后台管理API
- **负责人**：后端开发
- **工期**：2天
- **交付物**：
  - 分佣记录查询API
  - 推广数据统计API
  - 下级用户管理API

#### 9. 系统管理员API
- **负责人**：后端开发
- **工期**：2天
- **交付物**：
  - 代理商管理API
  - 分佣统计报表API
  - 系统配置管理API

### 第五阶段：测试验证（1周）

#### 10. 测试与验证
- **负责人**：后端开发 + 测试
- **工期**：3天
- **交付物**：
  - 单元测试用例
  - 集成测试用例
  - 功能验证报告

## 📊 核心数据表设计

### 代理商表 (agents)
```sql
CREATE TABLE `agents` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `agent_code` VARCHAR(20) UNIQUE NOT NULL COMMENT '代理商编码',
  `agent_name` VARCHAR(100) NOT NULL COMMENT '代理商名称',
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `contact_email` VARCHAR(100) COMMENT '联系邮箱',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `commission_rate_register` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '注册分佣比例',
  `commission_rate_print` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '打印分佣比例',
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  `delete_time` DATETIME NULL COMMENT '软删除时间'
);
```

### 推广码表 (promotion_codes)
```sql
CREATE TABLE `promotion_codes` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `agent_id` INT NOT NULL COMMENT '代理商ID',
  `code` VARCHAR(20) UNIQUE NOT NULL COMMENT '推广码',
  `name` VARCHAR(100) COMMENT '推广码名称',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `max_usage` INT DEFAULT 0 COMMENT '最大使用次数，0表示无限制',
  `current_usage` INT DEFAULT 0 COMMENT '当前使用次数',
  `expire_time` DATETIME NULL COMMENT '过期时间',
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  `delete_time` DATETIME NULL COMMENT '软删除时间'
);
```

### 分佣记录表 (commission_records)
```sql
CREATE TABLE `commission_records` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `agent_id` INT NOT NULL COMMENT '代理商ID',
  `promotion_code_id` INT NOT NULL COMMENT '推广码ID',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `commission_type` VARCHAR(20) NOT NULL COMMENT '分佣类型：REGISTER-注册，FIRST_PRINT-首次打印',
  `commission_amount` DECIMAL(10,2) NOT NULL COMMENT '分佣金额',
  `commission_rate` DECIMAL(5,4) NOT NULL COMMENT '分佣比例',
  `base_amount` DECIMAL(10,2) COMMENT '基础金额',
  `status` VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PENDING-待处理，CONFIRMED-已确认，PAID-已支付',
  `remark` TEXT COMMENT '备注',
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL
);
```

## 🔧 技术实施要点

### 1. 数据库设计原则
- 遵循项目现有命名规范
- 支持软删除机制
- 合理的索引设计
- 分佣记录的审计追踪

### 2. API设计原则
- 遵循项目现有的Controller/Service架构
- 集成现有的用户认证体系
- 使用@OperationLog注解记录关键操作
- 统一的RetKit返回格式

### 3. 业务逻辑设计
- 分佣规则的可配置化
- 防重复分佣机制
- 分佣状态的完整生命周期管理
- 异步分佣处理提升性能

## 📅 开发时间线

| 阶段 | 任务 | 预计工期 | 开始时间 | 结束时间 |
|------|------|----------|----------|----------|
| 第一阶段 | 数据库设计与建表 | 2-3天 | 第1天 | 第3天 |
| 第一阶段 | 数据模型层开发 | 2天 | 第4天 | 第5天 |
| 第二阶段 | 推广码管理服务 | 3天 | 第6天 | 第8天 |
| 第二阶段 | 代理商管理服务 | 3天 | 第9天 | 第11天 |
| 第二阶段 | 分佣计算服务 | 4天 | 第12天 | 第15天 |
| 第三阶段 | 用户推广码绑定API | 2天 | 第16天 | 第17天 |
| 第三阶段 | 分佣触发机制 | 3天 | 第18天 | 第20天 |
| 第四阶段 | 代理商后台管理API | 2天 | 第21天 | 第22天 |
| 第四阶段 | 系统管理员API | 2天 | 第23天 | 第24天 |
| 第五阶段 | 测试与验证 | 3天 | 第25天 | 第27天 |

**总工期：约4周（27个工作日）**

## 🎯 关键里程碑

1. **第1周末**：完成数据库设计和基础模型
2. **第2周末**：完成核心业务服务开发
3. **第3周末**：完成API接口和分佣触发机制
4. **第4周末**：完成管理功能和测试验证

## 📝 文档交付清单

- [ ] 数据库设计文档
- [ ] API接口文档
- [ ] 业务流程文档
- [ ] 部署指南
- [ ] 测试报告
- [ ] 用户使用手册

## 🔍 风险评估

### 高风险项
- 分佣计算逻辑的准确性
- 防重复分佣机制的可靠性
- 大量用户同时绑定推广码的性能

### 中风险项
- 与现有用户系统的集成复杂度
- 推广码生成算法的唯一性保证

### 低风险项
- 基础CRUD操作的实现
- 数据库表结构设计

## 📞 联系方式

如有任何问题或需要澄清需求，请及时沟通。

---
*文档版本：v1.0*  
*创建时间：2025-07-29*  
*最后更新：2025-07-29*
