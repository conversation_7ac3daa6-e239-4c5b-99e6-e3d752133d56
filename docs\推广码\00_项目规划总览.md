# 推广码系统 - 项目规划总览

## 📋 项目概述

### 核心需求
1. **用户绑定推广码**：用户注册后，在个人界面绑定"推广码"
2. **推广码管理**：系统管理员可以管理推广码
3. **数据记录**：记录用户绑定推广码的信息，供第三方SDK对接使用

### 技术架构
- **后端框架**：JFinal
- **数据库**：MySQL
- **ORM**：JFinal ActiveRecord
- **认证体系**：集成现有用户认证系统
- **第三方对接**：通过SDK处理分佣逻辑

## 🗂️ 任务分解

### 第一阶段：基础架构（3-4天）

#### 1. 数据库设计与建表
- **负责人**：后端开发
- **工期**：1天
- **交付物**：
  - 数据库设计文档
  - 数据库迁移脚本
  - 表结构验证脚本

#### 2. 数据模型层开发
- **负责人**：后端开发
- **工期**：1天
- **交付物**：
  - UserPromotionCode模型类
  - 模型映射配置

### 第二阶段：核心业务（2-3天）

#### 3. 推广码管理服务
- **负责人**：后端开发
- **工期**：2天
- **交付物**：
  - 推广码验证服务
  - 推广码绑定逻辑
  - 推广码查询服务

### 第三阶段：API接口（2-3天）

#### 4. 用户推广码绑定API
- **负责人**：后端开发
- **工期**：2天
- **交付物**：
  - 绑定推广码接口
  - 验证推广码接口
  - 查询绑定状态接口

#### 5. 系统管理员API
- **负责人**：后端开发
- **工期**：1天
- **交付物**：
  - 推广码管理API
  - 用户推广码查询API

### 第四阶段：测试验证（1-2天）

#### 6. 测试与验证
- **负责人**：后端开发
- **工期**：1天
- **交付物**：
  - 单元测试用例
  - 功能验证报告

## 📊 核心数据表设计

### 用户推广码表 (user_promotion_codes)
```sql
CREATE TABLE `user_promotion_codes` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `user_id` INT NOT NULL COMMENT '用户ID',
  `promotion_code` VARCHAR(50) NOT NULL COMMENT '推广码',
  `bind_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `bind_source` VARCHAR(20) DEFAULT 'MANUAL' COMMENT '绑定来源：MANUAL-手动绑定',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-有效，0-无效',
  `remark` TEXT COMMENT '备注信息',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_user_id` (`user_id`),
  INDEX `idx_promotion_code` (`promotion_code`),
  INDEX `idx_bind_time` (`bind_time`),
  FOREIGN KEY (`user_id`) REFERENCES `user`(`userId`)
);
```

## 🔧 技术实施要点

### 1. 数据库设计原则
- 遵循项目现有命名规范
- 简化表结构，只保存必要信息
- 合理的索引设计
- 用户推广码绑定的唯一性约束

### 2. API设计原则
- 遵循项目现有的Controller/Service架构
- 集成现有的用户认证体系
- 使用@OperationLog注解记录关键操作
- 统一的RetKit返回格式

### 3. 业务逻辑设计
- 简单的推广码验证机制
- 防重复绑定机制
- 为第三方SDK对接预留数据接口

## 📅 开发时间线

| 阶段 | 任务 | 预计工期 | 开始时间 | 结束时间 |
|------|------|----------|----------|----------|
| 第一阶段 | 数据库设计与建表 | 1天 | 第1天 | 第1天 |
| 第一阶段 | 数据模型层开发 | 1天 | 第2天 | 第2天 |
| 第二阶段 | 推广码管理服务 | 2天 | 第3天 | 第4天 |
| 第三阶段 | 用户推广码绑定API | 2天 | 第5天 | 第6天 |
| 第三阶段 | 系统管理员API | 1天 | 第7天 | 第7天 |
| 第四阶段 | 测试与验证 | 1天 | 第8天 | 第8天 |

**总工期：约1.5周（8个工作日）**

## 🎯 关键里程碑

1. **第1-2天**：完成数据库设计和基础模型
2. **第3-4天**：完成核心业务服务开发
3. **第5-7天**：完成API接口开发
4. **第8天**：完成测试验证

## 📝 文档交付清单

- [ ] 数据库设计文档
- [ ] API接口文档
- [ ] 业务流程文档
- [ ] 部署指南
- [ ] 测试报告
- [ ] 用户使用手册

## 🔍 风险评估

### 高风险项
- 分佣计算逻辑的准确性
- 防重复分佣机制的可靠性
- 大量用户同时绑定推广码的性能

### 中风险项
- 与现有用户系统的集成复杂度
- 推广码生成算法的唯一性保证

### 低风险项
- 基础CRUD操作的实现
- 数据库表结构设计

## 📞 联系方式

如有任何问题或需要澄清需求，请及时沟通。

---
*文档版本：v1.0*  
*创建时间：2025-07-29*  
*最后更新：2025-07-29*
