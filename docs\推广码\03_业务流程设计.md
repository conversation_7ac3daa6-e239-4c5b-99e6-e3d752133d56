# 推广码和代理商分佣系统 - 业务流程设计

## 🔄 核心业务流程

### 1. 用户绑定推广码流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API接口
    participant PS as 推广码服务
    participant CS as 分佣服务
    participant DB as 数据库

    U->>API: 输入推广码
    API->>PS: 验证推广码
    PS->>DB: 查询推广码信息
    DB-->>PS: 返回推广码详情
    
    alt 推广码有效
        PS->>DB: 检查用户绑定状态
        DB-->>PS: 返回绑定状态
        
        alt 用户未绑定
            PS->>DB: 创建绑定记录
            PS->>CS: 触发注册分佣
            CS->>DB: 创建分佣记录
            DB-->>API: 绑定成功
            API-->>U: 返回成功结果
        else 用户已绑定
            API-->>U: 返回已绑定错误
        end
    else 推广码无效
        API-->>U: 返回推广码无效错误
    end
```

### 2. 首次打印分佣流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant PA as 打印API
    participant PS as 打印服务
    participant CS as 分佣服务
    participant DB as 数据库

    U->>PA: 发起打印请求
    PA->>PS: 执行打印逻辑
    PS->>DB: 保存打印记录
    
    PS->>CS: 检查是否首次打印
    CS->>DB: 查询用户打印历史
    DB-->>CS: 返回打印历史
    
    alt 首次打印且已绑定推广码
        CS->>DB: 查询推广码绑定
        DB-->>CS: 返回绑定信息
        CS->>CS: 计算分佣金额
        CS->>DB: 创建分佣记录
        CS->>DB: 更新打印记录状态
    end
    
    PS-->>PA: 打印完成
    PA-->>U: 返回打印结果
```

### 3. 代理商推广码管理流程

```mermaid
flowchart TD
    A[代理商登录] --> B[查看推广码列表]
    B --> C{需要创建新推广码?}
    C -->|是| D[填写推广码信息]
    C -->|否| E[管理现有推广码]
    
    D --> F[系统生成推广码]
    F --> G[保存推广码信息]
    G --> H[推广码创建成功]
    
    E --> I{操作类型}
    I -->|编辑| J[修改推广码信息]
    I -->|禁用| K[禁用推广码]
    I -->|启用| L[启用推广码]
    
    J --> M[保存修改]
    K --> N[更新状态为禁用]
    L --> O[更新状态为启用]
    
    M --> P[操作完成]
    N --> P
    O --> P
    H --> P
```

## 📊 分佣计算规则

### 1. 注册分佣规则

**触发条件：**
- 用户成功绑定推广码
- 用户为新注册用户（首次绑定）
- 推广码状态为启用
- 代理商状态为启用

**计算公式：**
```
注册分佣金额 = 基础注册分佣金额 × 代理商注册分佣比例
```

**默认配置：**
- 基础注册分佣金额：10.00元
- 代理商注册分佣比例：由代理商配置决定（如10%）

**业务逻辑：**
```java
public CommissionRecord calculateRegisterCommission(User user, PromotionCode promotionCode) {
    // 1. 检查是否已经有注册分佣记录
    if (hasRegisterCommission(user.getUserId(), promotionCode.getId())) {
        return null; // 防止重复分佣
    }
    
    // 2. 获取分佣配置
    BigDecimal baseAmount = getConfigValue("default_register_amount");
    BigDecimal commissionRate = promotionCode.getAgent().getCommissionRateRegister();
    
    // 3. 计算分佣金额
    BigDecimal commissionAmount = baseAmount.multiply(commissionRate);
    
    // 4. 创建分佣记录
    return createCommissionRecord(user, promotionCode, "REGISTER", commissionAmount, commissionRate, baseAmount);
}
```

### 2. 首次打印分佣规则

**触发条件：**
- 用户已绑定推广码
- 用户首次执行打印操作
- 打印操作成功完成
- 推广码和代理商状态均为启用

**计算公式：**
```
首次打印分佣金额 = 基础打印分佣金额 × 代理商打印分佣比例
```

**默认配置：**
- 基础打印分佣金额：5.00元
- 代理商打印分佣比例：由代理商配置决定（如5%）

**业务逻辑：**
```java
public CommissionRecord calculateFirstPrintCommission(User user, String printRecordId) {
    // 1. 检查是否为首次打印
    if (!isFirstPrint(user.getUserId())) {
        return null;
    }
    
    // 2. 检查用户是否绑定推广码
    UserPromotionBinding binding = getActiveBinding(user.getUserId());
    if (binding == null) {
        return null;
    }
    
    // 3. 检查是否已有首次打印分佣
    if (hasFirstPrintCommission(user.getUserId())) {
        return null; // 防止重复分佣
    }
    
    // 4. 获取分佣配置
    BigDecimal baseAmount = getConfigValue("default_print_amount");
    BigDecimal commissionRate = binding.getAgent().getCommissionRatePrint();
    
    // 5. 计算分佣金额
    BigDecimal commissionAmount = baseAmount.multiply(commissionRate);
    
    // 6. 创建分佣记录
    return createCommissionRecord(user, binding.getPromotionCode(), "FIRST_PRINT", 
                                commissionAmount, commissionRate, baseAmount, printRecordId);
}
```

## 🔐 防重复分佣机制

### 1. 数据库约束
```sql
-- 防止同一用户重复注册分佣
CREATE UNIQUE INDEX `uk_user_register_commission` 
ON `commission_records`(`user_id`, `commission_type`) 
WHERE `commission_type` = 'REGISTER';

-- 防止同一用户重复首次打印分佣
CREATE UNIQUE INDEX `uk_user_first_print_commission` 
ON `commission_records`(`user_id`, `commission_type`) 
WHERE `commission_type` = 'FIRST_PRINT';
```

### 2. 业务逻辑检查
```java
public boolean hasRegisterCommission(Integer userId, Integer promotionCodeId) {
    return CommissionRecord.dao.findFirst(
        "SELECT * FROM commission_records WHERE user_id = ? AND commission_type = 'REGISTER'", 
        userId) != null;
}

public boolean hasFirstPrintCommission(Integer userId) {
    return CommissionRecord.dao.findFirst(
        "SELECT * FROM commission_records WHERE user_id = ? AND commission_type = 'FIRST_PRINT'", 
        userId) != null;
}
```

### 3. 分布式锁（高并发场景）
```java
@Service
public class CommissionService {
    
    @Autowired
    private RedisTemplate redisTemplate;
    
    public CommissionRecord processCommission(User user, String commissionType) {
        String lockKey = "commission_lock:" + user.getUserId() + ":" + commissionType;
        
        try {
            // 获取分布式锁
            Boolean lockAcquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, "1", Duration.ofMinutes(1));
            
            if (!lockAcquired) {
                throw new BusinessException("分佣处理中，请稍后重试");
            }
            
            // 执行分佣逻辑
            return calculateCommission(user, commissionType);
            
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }
}
```

## 📈 分佣状态管理

### 1. 分佣状态流转

```mermaid
stateDiagram-v2
    [*] --> PENDING: 创建分佣记录
    PENDING --> CONFIRMED: 管理员确认
    PENDING --> CANCELLED: 取消分佣
    CONFIRMED --> PAID: 完成支付
    CONFIRMED --> CANCELLED: 取消分佣
    CANCELLED --> [*]
    PAID --> [*]
```

### 2. 状态变更规则

| 当前状态 | 可变更状态 | 操作权限 | 说明 |
|----------|------------|----------|------|
| PENDING | CONFIRMED | 管理员 | 确认分佣有效 |
| PENDING | CANCELLED | 管理员/系统 | 取消无效分佣 |
| CONFIRMED | PAID | 管理员 | 标记已支付 |
| CONFIRMED | CANCELLED | 管理员 | 取消已确认分佣 |

### 3. 自动状态处理
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void autoConfirmCommissions() {
    // 自动确认超过24小时的待处理分佣记录
    List<CommissionRecord> pendingRecords = CommissionRecord.dao.find(
        "SELECT * FROM commission_records WHERE status = 'PENDING' AND create_time < ?",
        DateUtil.offsetHour(new Date(), -24)
    );
    
    for (CommissionRecord record : pendingRecords) {
        record.setStatus("CONFIRMED").setUpdateTime(new Date()).update();
    }
}
```

## 🔄 异步分佣处理

### 1. 异步处理架构

```mermaid
flowchart LR
    A[业务触发点] --> B[发送分佣事件]
    B --> C[消息队列]
    C --> D[分佣处理器]
    D --> E[计算分佣]
    E --> F[保存记录]
    F --> G[发送通知]
```

### 2. 事件驱动实现
```java
// 分佣事件
public class CommissionEvent {
    private Integer userId;
    private String commissionType;
    private String businessId;
    private Map<String, Object> extraData;
    // getters and setters
}

// 事件发布
@Component
public class CommissionEventPublisher {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    public void publishCommissionEvent(Integer userId, String commissionType, String businessId) {
        CommissionEvent event = new CommissionEvent();
        event.setUserId(userId);
        event.setCommissionType(commissionType);
        event.setBusinessId(businessId);
        
        eventPublisher.publishEvent(event);
    }
}

// 事件监听器
@Component
public class CommissionEventListener {
    
    @Autowired
    private CommissionService commissionService;
    
    @EventListener
    @Async
    public void handleCommissionEvent(CommissionEvent event) {
        try {
            commissionService.processCommission(event);
        } catch (Exception e) {
            // 记录错误日志，可以考虑重试机制
            LogKit.error("分佣处理失败", e);
        }
    }
}
```

## 📊 数据统计与报表

### 1. 实时统计指标
- 代理商推广用户数
- 代理商累计分佣金额
- 推广码使用情况
- 分佣记录状态分布

### 2. 定时统计任务
```java
@Scheduled(cron = "0 30 1 * * ?") // 每天凌晨1:30执行
public void generateDailyStatistics() {
    // 生成昨日统计数据
    Date yesterday = DateUtil.offsetDay(new Date(), -1);
    
    // 统计各代理商昨日新增用户数
    // 统计各代理商昨日分佣金额
    // 更新推广码使用统计
}
```

### 3. 报表数据结构
```java
public class AgentStatistics {
    private Integer agentId;
    private String agentName;
    private Integer totalUsers;        // 总推广用户数
    private BigDecimal totalCommission; // 总分佣金额
    private Integer todayUsers;        // 今日新增用户
    private BigDecimal todayCommission; // 今日分佣金额
    private List<PromotionCodeStats> promotionCodeStats; // 推广码统计
}
```

---
*文档版本：v1.0*  
*创建时间：2025-07-29*  
*最后更新：2025-07-29*
