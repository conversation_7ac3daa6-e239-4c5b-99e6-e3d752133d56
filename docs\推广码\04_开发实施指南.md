# 推广码和代理商分佣系统 - 开发实施指南

## 🚀 开发环境准备

### 1. 开发工具要求
- **IDE**: IntelliJ IDEA 或 Eclipse
- **JDK**: Java 8+
- **数据库**: MySQL 5.7+
- **构建工具**: Maven 3.6+
- **版本控制**: Git

### 2. 项目依赖检查
确保项目中已包含以下依赖：
```xml
<!-- JFinal框架 -->
<dependency>
    <groupId>com.jfinal</groupId>
    <artifactId>jfinal</artifactId>
</dependency>

<!-- MySQL驱动 -->
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
</dependency>

<!-- Hutool工具包 -->
<dependency>
    <groupId>cn.hutool</groupId>
    <artifactId>hutool-all</artifactId>
</dependency>
```

## 📋 开发任务清单

### 第一阶段：数据库和模型层（第1-5天）

#### ✅ 任务1：数据库设计与建表（第1-3天）
- [ ] 创建数据库迁移脚本 `V2__create_promotion_system_tables.sql`
- [ ] 执行数据库迁移
- [ ] 验证表结构和索引
- [ ] 插入初始化配置数据

**实施步骤：**
1. 在 `src/main/resources/db/migration/` 目录创建迁移脚本
2. 参考 `docs/推广码/01_数据库设计.md` 中的表结构
3. 执行迁移脚本并验证

#### ✅ 任务2：数据模型层开发（第4-5天）
- [ ] 运行 `GeneratorModel.main()` 生成基础模型
- [ ] 创建业务模型类
- [ ] 更新 `_MappingKit.java`
- [ ] 编写模型单元测试

**实施步骤：**
1. 修改 `GeneratorModel.java` 排除新表的自动生成（如需要）
2. 手动创建或生成模型类
3. 添加业务方法到模型类中

### 第二阶段：核心服务层（第6-15天）

#### ✅ 任务3：推广码管理服务（第6-8天）
**文件位置：** `src/main/java/com/sandu/xinye/api/v2/promotion/`

- [ ] 创建 `PromotionCodeService.java`
- [ ] 实现推广码生成算法
- [ ] 实现推广码验证逻辑
- [ ] 实现推广码状态管理

**核心方法：**
```java
public class PromotionCodeService {
    public static final PromotionCodeService me = new PromotionCodeService();
    
    // 生成推广码
    public String generatePromotionCode(Integer agentId);
    
    // 验证推广码
    public RetKit validatePromotionCode(String code);
    
    // 绑定推广码
    public RetKit bindPromotionCode(Integer userId, String code);
    
    // 解绑推广码
    public RetKit unbindPromotionCode(Integer userId);
}
```

#### ✅ 任务4：代理商管理服务（第9-11天）
**文件位置：** `src/main/java/com/sandu/xinye/api/v2/agent/`

- [ ] 创建 `AgentService.java`
- [ ] 实现代理商注册逻辑
- [ ] 实现代理商认证机制
- [ ] 实现代理商信息管理

**核心方法：**
```java
public class AgentService {
    public static final AgentService me = new AgentService();
    
    // 代理商登录
    public RetKit login(String agentCode, String password);
    
    // 创建推广码
    public RetKit createPromotionCode(Integer agentId, String name, String description);
    
    // 获取推广码列表
    public RetKit getPromotionCodes(Integer agentId, int pageNumber, int pageSize);
}
```

#### ✅ 任务5：分佣计算服务（第12-15天）
**文件位置：** `src/main/java/com/sandu/xinye/api/v2/commission/`

- [ ] 创建 `CommissionService.java`
- [ ] 实现分佣规则引擎
- [ ] 实现注册分佣计算
- [ ] 实现首次打印分佣计算
- [ ] 实现防重复分佣机制

**核心方法：**
```java
public class CommissionService {
    public static final CommissionService me = new CommissionService();
    
    // 处理注册分佣
    public CommissionRecord processRegisterCommission(Integer userId, Integer promotionCodeId);
    
    // 处理首次打印分佣
    public CommissionRecord processFirstPrintCommission(Integer userId, String printRecordId);
    
    // 查询分佣记录
    public RetKit getCommissionRecords(Integer agentId, int pageNumber, int pageSize);
}
```

### 第三阶段：API接口层（第16-20天）

#### ✅ 任务6：用户推广码绑定API（第16-17天）
**文件位置：** `src/main/java/com/sandu/xinye/api/v2/promotion/PromotionController.java`

- [ ] 创建控制器类
- [ ] 实现绑定推广码接口
- [ ] 实现验证推广码接口
- [ ] 实现查询绑定状态接口
- [ ] 添加操作日志注解

**示例代码：**
```java
public class PromotionController extends AppController {
    
    @OperationLog(modelName = "promotion")
    public void bindCode() {
        String promotionCode = getPara("promotionCode");
        Integer userId = getUser().getUserId();
        RetKit ret = PromotionCodeService.me.bindPromotionCode(userId, promotionCode);
        renderJson(ret);
    }
    
    @Clear
    public void validateCode() {
        String code = getPara("code");
        RetKit ret = PromotionCodeService.me.validatePromotionCode(code);
        renderJson(ret);
    }
}
```

#### ✅ 任务7：分佣触发机制（第18-20天）
- [ ] 在用户注册流程中集成分佣触发
- [ ] 在首次打印逻辑中集成分佣触发
- [ ] 实现异步分佣处理
- [ ] 添加分佣事件监听器

**集成点1：用户注册后绑定推广码**
```java
// 在 UserLoginService.register() 方法中添加
public RetKit register(String phone, String password, String captcha, String ipAddress) {
    // ... 原有注册逻辑
    
    if (succ) {
        // 检查是否有推广码需要绑定（可以通过session或其他方式传递）
        String promotionCode = getPromotionCodeFromSession();
        if (StrKit.notBlank(promotionCode)) {
            PromotionCodeService.me.bindPromotionCode(newUser.getUserId(), promotionCode);
        }
    }
    
    return succ ? RetKit.ok() : RetKit.fail();
}
```

**集成点2：首次打印分佣**
```java
// 在 TempletService.addTemplet() 方法中添加
public RetKit addTemplet(/* 参数 */) {
    // ... 原有保存逻辑
    
    if (succ) {
        // 检查是否为首次打印
        CommissionService.me.processFirstPrintCommission(userId, model.getId().toString());
    }
    
    return succ ? RetKit.ok() : RetKit.fail();
}
```

### 第四阶段：管理功能（第21-24天）

#### ✅ 任务8：代理商后台管理API（第21-22天）
**文件位置：** `src/main/java/com/sandu/xinye/api/v2/agent/AgentController.java`

- [ ] 创建代理商控制器
- [ ] 实现代理商登录接口
- [ ] 实现推广码管理接口
- [ ] 实现分佣记录查询接口
- [ ] 实现推广数据统计接口

#### ✅ 任务9：系统管理员API（第23-24天）
**文件位置：** `src/main/java/com/sandu/xinye/admin/promotion/`

- [ ] 创建管理员控制器
- [ ] 实现代理商管理接口
- [ ] 实现分佣统计报表接口
- [ ] 实现系统配置管理接口

### 第五阶段：测试验证（第25-27天）

#### ✅ 任务10：测试与验证（第25-27天）
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 执行功能验证
- [ ] 性能测试
- [ ] 编写测试报告

## 🔧 开发规范

### 1. 代码规范
- 遵循项目现有的代码风格
- 使用有意义的变量和方法名
- 添加必要的注释和文档
- 遵循单一职责原则

### 2. 数据库规范
- 表名使用下划线命名法
- 字段名使用驼峰命名法（与现有项目保持一致）
- 必须添加字段注释
- 合理使用索引

### 3. API规范
- 遵循RESTful设计原则
- 使用统一的响应格式（RetKit）
- 添加适当的HTTP状态码
- 实现统一的异常处理

### 4. 日志规范
- 关键操作使用@OperationLog注解
- 错误信息使用LogKit.error记录
- 调试信息使用LogKit.debug记录
- 业务日志使用LogKit.info记录

## 📁 文件结构规划

```
src/main/java/com/sandu/xinye/
├── api/v2/
│   ├── promotion/
│   │   ├── PromotionController.java
│   │   └── PromotionCodeService.java
│   ├── agent/
│   │   ├── AgentController.java
│   │   └── AgentService.java
│   └── commission/
│       ├── CommissionController.java
│       └── CommissionService.java
├── admin/promotion/
│   ├── AdminPromotionController.java
│   └── AdminPromotionService.java
├── common/
│   ├── model/
│   │   ├── Agent.java
│   │   ├── PromotionCode.java
│   │   ├── UserPromotionBinding.java
│   │   ├── CommissionRecord.java
│   │   └── CommissionConfig.java
│   ├── enums/
│   │   ├── CommissionType.java
│   │   └── CommissionStatus.java
│   └── interceptor/
│       └── AgentAuthInterceptor.java
└── test/
    ├── promotion/
    │   ├── PromotionCodeServiceTest.java
    │   └── CommissionServiceTest.java
    └── integration/
        └── PromotionIntegrationTest.java
```

## 🔍 关键技术点

### 1. 推广码生成算法
```java
public String generatePromotionCode(Integer agentId) {
    // 使用时间戳 + 代理商ID + 随机数生成唯一推广码
    String timestamp = String.valueOf(System.currentTimeMillis()).substring(8);
    String agentPart = String.format("%04d", agentId);
    String randomPart = RandomKit.getRandomCharAndNum(4);
    
    return "P" + timestamp + agentPart + randomPart;
}
```

### 2. 分佣计算精度处理
```java
// 使用BigDecimal确保计算精度
public BigDecimal calculateCommission(BigDecimal baseAmount, BigDecimal rate) {
    return baseAmount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
}
```

### 3. 防重复分佣检查
```java
public boolean isDuplicateCommission(Integer userId, String commissionType) {
    String sql = "SELECT COUNT(*) FROM commission_records WHERE user_id = ? AND commission_type = ?";
    Long count = Db.queryLong(sql, userId, commissionType);
    return count > 0;
}
```

## 📊 性能优化建议

### 1. 数据库优化
- 合理使用索引
- 分页查询大数据量
- 使用连接池优化数据库连接

### 2. 缓存策略
- 推广码验证结果缓存
- 代理商信息缓存
- 分佣配置缓存

### 3. 异步处理
- 分佣计算异步处理
- 统计数据异步更新
- 通知消息异步发送

## 🚨 注意事项

### 1. 数据一致性
- 使用事务确保数据一致性
- 实现幂等性防止重复操作
- 添加数据校验机制

### 2. 安全考虑
- 推广码绑定防刷机制
- 敏感操作权限验证
- 数据传输加密

### 3. 错误处理
- 统一异常处理机制
- 友好的错误提示信息
- 完整的错误日志记录

---
*文档版本：v1.0*  
*创建时间：2025-07-29*  
*最后更新：2025-07-29*
