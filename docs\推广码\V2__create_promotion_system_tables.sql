-- XPrinter推广码和代理商分佣系统 - 数据库迁移脚本 V2.0
-- 创建时间: 2025-07-29
-- 说明: 创建推广码和代理商分佣系统相关表

USE xinye_app_v3;

-- =====================================================
-- 第一部分：创建代理商表
-- =====================================================

CREATE TABLE `agents` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '代理商ID',
  `agent_code` VARCHAR(20) UNIQUE NOT NULL COMMENT '代理商编码',
  `agent_name` VARCHAR(100) NOT NULL COMMENT '代理商名称',
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `contact_email` VARCHAR(100) COMMENT '联系邮箱',
  `password` VARCHAR(64) COMMENT '登录密码（SHA256加密）',
  `salt` VARCHAR(32) COMMENT '密码盐值',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `commission_rate_register` DECIMAL(5,4) DEFAULT 0.1000 COMMENT '注册分佣比例',
  `commission_rate_print` DECIMAL(5,4) DEFAULT 0.0500 COMMENT '打印分佣比例',
  `remark` TEXT COMMENT '备注信息',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` DATETIME NULL COMMENT '软删除时间',
  INDEX `idx_agent_code` (`agent_code`),
  INDEX `idx_status` (`status`),
  INDEX `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理商表';

-- =====================================================
-- 第二部分：创建推广码表
-- =====================================================

CREATE TABLE `promotion_codes` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '推广码ID',
  `agent_id` INT NOT NULL COMMENT '代理商ID',
  `code` VARCHAR(20) UNIQUE NOT NULL COMMENT '推广码',
  `name` VARCHAR(100) COMMENT '推广码名称',
  `description` TEXT COMMENT '推广码描述',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `max_usage` INT DEFAULT 0 COMMENT '最大使用次数，0表示无限制',
  `current_usage` INT DEFAULT 0 COMMENT '当前使用次数',
  `expire_time` DATETIME NULL COMMENT '过期时间，NULL表示永不过期',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` DATETIME NULL COMMENT '软删除时间',
  INDEX `idx_agent_id` (`agent_id`),
  INDEX `idx_code` (`code`),
  INDEX `idx_status` (`status`),
  INDEX `idx_expire_time` (`expire_time`),
  INDEX `idx_delete_time` (`delete_time`),
  FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广码表';

-- =====================================================
-- 第三部分：创建用户推广码绑定表
-- =====================================================

CREATE TABLE `user_promotion_bindings` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '绑定记录ID',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `promotion_code_id` INT NOT NULL COMMENT '推广码ID',
  `agent_id` INT NOT NULL COMMENT '代理商ID（冗余字段，便于查询）',
  `bind_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `unbind_time` DATETIME NULL COMMENT '解绑时间，NULL表示未解绑',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-已绑定，0-已解绑',
  `bind_source` VARCHAR(20) DEFAULT 'MANUAL' COMMENT '绑定来源：MANUAL-手动绑定，AUTO-自动绑定',
  `remark` TEXT COMMENT '备注信息',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_promotion_code_id` (`promotion_code_id`),
  INDEX `idx_agent_id` (`agent_id`),
  INDEX `idx_bind_time` (`bind_time`),
  INDEX `idx_status` (`status`),
  INDEX `idx_user_binding_active` (`user_id`, `status`, `bind_time`),
  UNIQUE KEY `uk_user_active` (`user_id`, `status`),
  FOREIGN KEY (`user_id`) REFERENCES `user`(`userId`),
  FOREIGN KEY (`promotion_code_id`) REFERENCES `promotion_codes`(`id`),
  FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户推广码绑定表';

-- =====================================================
-- 第四部分：创建分佣记录表
-- =====================================================

CREATE TABLE `commission_records` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分佣记录ID',
  `agent_id` INT NOT NULL COMMENT '代理商ID',
  `promotion_code_id` INT NOT NULL COMMENT '推广码ID',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `commission_type` VARCHAR(20) NOT NULL COMMENT '分佣类型：REGISTER-注册，FIRST_PRINT-首次打印',
  `commission_amount` DECIMAL(10,2) NOT NULL COMMENT '分佣金额',
  `commission_rate` DECIMAL(5,4) NOT NULL COMMENT '分佣比例',
  `base_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '基础金额',
  `status` VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PENDING-待处理，CONFIRMED-已确认，PAID-已支付，CANCELLED-已取消',
  `business_id` VARCHAR(50) COMMENT '业务ID（如打印记录ID）',
  `business_type` VARCHAR(20) COMMENT '业务类型（如PRINT）',
  `settlement_batch` VARCHAR(50) COMMENT '结算批次号',
  `settlement_time` DATETIME NULL COMMENT '结算时间',
  `remark` TEXT COMMENT '备注信息',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_agent_id` (`agent_id`),
  INDEX `idx_promotion_code_id` (`promotion_code_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_commission_type` (`commission_type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`),
  INDEX `idx_settlement_batch` (`settlement_batch`),
  INDEX `idx_business` (`business_type`, `business_id`),
  INDEX `idx_agent_commission_time` (`agent_id`, `create_time`, `status`),
  FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`),
  FOREIGN KEY (`promotion_code_id`) REFERENCES `promotion_codes`(`id`),
  FOREIGN KEY (`user_id`) REFERENCES `user`(`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣记录表';

-- =====================================================
-- 第五部分：创建分佣配置表
-- =====================================================

CREATE TABLE `commission_configs` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
  `config_key` VARCHAR(50) UNIQUE NOT NULL COMMENT '配置键',
  `config_value` TEXT NOT NULL COMMENT '配置值',
  `config_type` VARCHAR(20) NOT NULL COMMENT '配置类型：RATE-比例，AMOUNT-金额，RULE-规则',
  `description` VARCHAR(200) COMMENT '配置描述',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_config_key` (`config_key`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣配置表';

-- =====================================================
-- 第六部分：添加业务约束
-- =====================================================

-- 确保分佣比例在合理范围内
ALTER TABLE `agents` ADD CONSTRAINT `chk_commission_rate_register` 
CHECK (`commission_rate_register` >= 0 AND `commission_rate_register` <= 1);

ALTER TABLE `agents` ADD CONSTRAINT `chk_commission_rate_print` 
CHECK (`commission_rate_print` >= 0 AND `commission_rate_print` <= 1);

-- 确保分佣金额为正数
ALTER TABLE `commission_records` ADD CONSTRAINT `chk_commission_amount` 
CHECK (`commission_amount` >= 0);

-- 代理商状态约束
ALTER TABLE `agents` ADD CONSTRAINT `chk_agent_status` 
CHECK (`status` IN (0, 1));

-- 推广码状态约束
ALTER TABLE `promotion_codes` ADD CONSTRAINT `chk_promotion_status` 
CHECK (`status` IN (0, 1));

-- 分佣记录状态约束
ALTER TABLE `commission_records` ADD CONSTRAINT `chk_commission_status` 
CHECK (`status` IN ('PENDING', 'CONFIRMED', 'PAID', 'CANCELLED'));

-- =====================================================
-- 第七部分：插入初始化配置数据
-- =====================================================

INSERT INTO `commission_configs` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('default_register_amount', '10.00', 'AMOUNT', '默认注册分佣基础金额'),
('default_print_amount', '5.00', 'AMOUNT', '默认首次打印分佣基础金额'),
('default_register_rate', '0.1000', 'RATE', '默认注册分佣比例'),
('default_print_rate', '0.0500', 'RATE', '默认打印分佣比例'),
('max_promotion_codes_per_agent', '10', 'RULE', '每个代理商最大推广码数量'),
('promotion_code_length', '12', 'RULE', '推广码长度'),
('commission_settlement_cycle', '30', 'RULE', '分佣结算周期（天）'),
('auto_confirm_hours', '24', 'RULE', '自动确认分佣时间（小时）');

-- =====================================================
-- 第八部分：插入测试数据（仅开发环境）
-- =====================================================

-- 测试代理商（密码为 123456 的SHA256加密值）
INSERT INTO `agents` (`agent_code`, `agent_name`, `contact_person`, `contact_phone`, `contact_email`, `password`, `salt`, `commission_rate_register`, `commission_rate_print`) VALUES
('AGENT001', '测试代理商A', '张三', '13800138001', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 'salt123', 0.1000, 0.0500),
('AGENT002', '测试代理商B', '李四', '13800138002', '<EMAIL>', 'e10adc3949ba59abbe56e057f20f883e', 'salt456', 0.0800, 0.0300);

-- 测试推广码
INSERT INTO `promotion_codes` (`agent_id`, `code`, `name`, `description`) VALUES
(1, 'PROMO001', '代理商A推广码1', '测试推广码，用于功能验证'),
(1, 'PROMO002', '代理商A推广码2', '测试推广码，用于功能验证'),
(2, 'PROMO003', '代理商B推广码1', '测试推广码，用于功能验证');

-- =====================================================
-- 第九部分：验证脚本
-- =====================================================

-- 验证表是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('agents', 'promotion_codes', 'user_promotion_bindings', 'commission_records', 'commission_configs')
ORDER BY TABLE_NAME;

-- 验证索引是否创建成功
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('agents', 'promotion_codes', 'user_promotion_bindings', 'commission_records', 'commission_configs')
  AND INDEX_NAME != 'PRIMARY'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 验证外键约束是否创建成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
  AND REFERENCED_TABLE_NAME IS NOT NULL
  AND TABLE_NAME IN ('promotion_codes', 'user_promotion_bindings', 'commission_records')
ORDER BY TABLE_NAME;

-- 验证初始化数据是否插入成功
SELECT '代理商数量' as item, COUNT(*) as count FROM agents
UNION ALL
SELECT '推广码数量' as item, COUNT(*) as count FROM promotion_codes
UNION ALL
SELECT '配置项数量' as item, COUNT(*) as count FROM commission_configs;

-- =====================================================
-- 第十部分：清理脚本（仅在需要时执行）
-- =====================================================

/*
-- 如果需要删除所有表，请执行以下脚本（谨慎操作）
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `commission_records`;
DROP TABLE IF EXISTS `user_promotion_bindings`;
DROP TABLE IF EXISTS `promotion_codes`;
DROP TABLE IF EXISTS `agents`;
DROP TABLE IF EXISTS `commission_configs`;
SET FOREIGN_KEY_CHECKS = 1;
*/
